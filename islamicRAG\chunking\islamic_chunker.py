"""English Quran text-aware chunking strategies."""

import re
from typing import List, Dict, Any

from ..core.config import ChunkingConfig


class IslamicChunker:
    """Chunker specialized for English Islamic texts (primarily Quran)."""

    def __init__(self, config: ChunkingConfig):
        self.config = config

        # Patterns for English Quran text structures
        self.verse_pattern = re.compile(r'(\d+:\d+|\{\d+\}|\(\d+\))')
        self.chapter_pattern = re.compile(r'(Chapter|Surah|Sura)\s+\d+', re.IGNORECASE)
        self.page_pattern = re.compile(r'--- Page \d+ ---')
    
    def chunk_text(self, text: str, metadata: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Chunk text using English Quran-aware strategies."""
        if metadata is None:
            metadata = {}

        text_type = metadata.get('type', 'general')

        if text_type == 'quran':
            return self._chunk_quran(text, metadata)
        else:
            return self._chunk_general(text, metadata)
    
    def _chunk_quran(self, text: str, metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Chunk Quran text respecting verse boundaries."""
        chunks = []
        
        if self.config.respect_verse_boundaries:
            # Split by verses
            verses = self._split_by_verses(text)
            
            current_chunk = ""
            current_verses = []
            
            for verse_num, verse_text in verses:
                # Check if adding this verse would exceed chunk size
                potential_chunk = current_chunk + f" {verse_text}"
                
                if len(potential_chunk.split()) > self.config.chunk_size and current_chunk:
                    # Save current chunk
                    chunks.append(self._create_chunk(
                        current_chunk.strip(),
                        {**metadata, 'verses': current_verses}
                    ))
                    
                    # Start new chunk
                    current_chunk = verse_text
                    current_verses = [verse_num]
                else:
                    current_chunk = potential_chunk
                    current_verses.append(verse_num)
            
            # Add final chunk
            if current_chunk.strip():
                chunks.append(self._create_chunk(
                    current_chunk.strip(),
                    {**metadata, 'verses': current_verses}
                ))
        else:
            chunks = self._chunk_general(text, metadata)
        
        return chunks
    

    
    def _chunk_general(self, text: str, metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """General chunking strategy."""
        chunks = []
        words = text.split()
        
        for i in range(0, len(words), self.config.chunk_size - self.config.chunk_overlap):
            chunk_words = words[i:i + self.config.chunk_size]
            chunk_text = " ".join(chunk_words)
            chunks.append(self._create_chunk(chunk_text, metadata))
        
        return chunks
    
    def _split_by_verses(self, text: str) -> List[tuple]:
        """Split text by verse numbers."""
        verses = []
        parts = self.verse_pattern.split(text)
        
        current_verse = None
        for part in parts:
            if self.verse_pattern.match(part):
                current_verse = part
            elif current_verse and part.strip():
                verses.append((current_verse, part.strip()))
                current_verse = None
        
        return verses
    

    
    def _chunk_by_sentences(self, text: str) -> List[str]:
        """Chunk text by sentences for English text."""
        sentences = re.split(r'[.!?]', text)
        chunks = []
        current_chunk = ""

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            potential_chunk = current_chunk + " " + sentence if current_chunk else sentence

            if len(potential_chunk.split()) > self.config.chunk_size and current_chunk:
                chunks.append(current_chunk.strip())
                current_chunk = sentence
            else:
                current_chunk = potential_chunk

        if current_chunk.strip():
            chunks.append(current_chunk.strip())

        return chunks
    
    def _create_chunk(self, text: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Create a chunk dictionary."""
        return {
            "content": text,
            "metadata": metadata,
            "word_count": len(text.split()),
            "char_count": len(text)
        }