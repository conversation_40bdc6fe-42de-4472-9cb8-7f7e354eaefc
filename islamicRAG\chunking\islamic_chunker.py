"""Islamic text-aware chunking strategies."""

import re
from typing import List, Dict, Any

from ..core.config import ChunkingConfig


class IslamicChunker:
    """Chunker specialized for Islamic texts (Quran, Hadith, etc.)."""
    
    def __init__(self, config: ChunkingConfig):
        self.config = config
        
        # Patterns for Islamic text structures
        self.verse_pattern = re.compile(r'(\d+:\d+|\{\d+\}|\(\d+\))')
        self.hadith_pattern = re.compile(r'(حدثنا|أخبرنا|عن|قال|رواه|صحيح|حسن|ضعيف)')
        self.chapter_pattern = re.compile(r'(سورة|باب|كتاب|فصل)')
    
    def chunk_text(self, text: str, metadata: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Chunk text using Islamic-aware strategies."""
        if metadata is None:
            metadata = {}
            
        text_type = metadata.get('type', 'general')
        
        if text_type == 'quran':
            return self._chunk_quran(text, metadata)
        elif text_type == 'hadith':
            return self._chunk_hadith(text, metadata)
        else:
            return self._chunk_general(text, metadata)
    
    def _chunk_quran(self, text: str, metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Chunk Quran text respecting verse boundaries."""
        chunks = []
        
        if self.config.respect_verse_boundaries:
            # Split by verses
            verses = self._split_by_verses(text)
            
            current_chunk = ""
            current_verses = []
            
            for verse_num, verse_text in verses:
                # Check if adding this verse would exceed chunk size
                potential_chunk = current_chunk + f" {verse_text}"
                
                if len(potential_chunk.split()) > self.config.chunk_size and current_chunk:
                    # Save current chunk
                    chunks.append(self._create_chunk(
                        current_chunk.strip(),
                        {**metadata, 'verses': current_verses}
                    ))
                    
                    # Start new chunk
                    current_chunk = verse_text
                    current_verses = [verse_num]
                else:
                    current_chunk = potential_chunk
                    current_verses.append(verse_num)
            
            # Add final chunk
            if current_chunk.strip():
                chunks.append(self._create_chunk(
                    current_chunk.strip(),
                    {**metadata, 'verses': current_verses}
                ))
        else:
            chunks = self._chunk_general(text, metadata)
        
        return chunks
    
    def _chunk_hadith(self, text: str, metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Chunk Hadith text respecting hadith structure."""
        chunks = []
        
        if self.config.respect_hadith_structure:
            # Split by hadith boundaries
            hadiths = self._split_by_hadith(text)
            
            for hadith_text in hadiths:
                if len(hadith_text.split()) > self.config.chunk_size:
                    # Split large hadith into smaller chunks
                    sub_chunks = self._chunk_by_sentences(hadith_text)
                    for sub_chunk in sub_chunks:
                        chunks.append(self._create_chunk(sub_chunk, metadata))
                else:
                    chunks.append(self._create_chunk(hadith_text, metadata))
        else:
            chunks = self._chunk_general(text, metadata)
        
        return chunks
    
    def _chunk_general(self, text: str, metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """General chunking strategy."""
        chunks = []
        words = text.split()
        
        for i in range(0, len(words), self.config.chunk_size - self.config.chunk_overlap):
            chunk_words = words[i:i + self.config.chunk_size]
            chunk_text = " ".join(chunk_words)
            chunks.append(self._create_chunk(chunk_text, metadata))
        
        return chunks
    
    def _split_by_verses(self, text: str) -> List[tuple]:
        """Split text by verse numbers."""
        verses = []
        parts = self.verse_pattern.split(text)
        
        current_verse = None
        for part in parts:
            if self.verse_pattern.match(part):
                current_verse = part
            elif current_verse and part.strip():
                verses.append((current_verse, part.strip()))
                current_verse = None
        
        return verses
    
    def _split_by_hadith(self, text: str) -> List[str]:
        """Split text by hadith boundaries."""
        # Look for hadith indicators
        parts = self.hadith_pattern.split(text)
        hadiths = []
        
        current_hadith = ""
        for part in parts:
            if self.hadith_pattern.match(part):
                if current_hadith.strip():
                    hadiths.append(current_hadith.strip())
                current_hadith = part
            else:
                current_hadith += " " + part
        
        if current_hadith.strip():
            hadiths.append(current_hadith.strip())
        
        return hadiths
    
    def _chunk_by_sentences(self, text: str) -> List[str]:
        """Chunk text by sentences."""
        sentences = re.split(r'[.!?؟۔]', text)
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
                
            potential_chunk = current_chunk + " " + sentence if current_chunk else sentence
            
            if len(potential_chunk.split()) > self.config.chunk_size and current_chunk:
                chunks.append(current_chunk.strip())
                current_chunk = sentence
            else:
                current_chunk = potential_chunk
        
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def _create_chunk(self, text: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Create a chunk dictionary."""
        return {
            "content": text,
            "metadata": metadata,
            "word_count": len(text.split()),
            "char_count": len(text)
        }