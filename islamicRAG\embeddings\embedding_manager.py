"""Embedding manager for Islamic texts with Arabic optimization."""

from typing import List, Union, Dict, Any
import torch
from sentence_transformers import SentenceTransformer
from loguru import logger
import arabic_reshaper
from bidi.algorithm import get_display

from ..core.config import EmbeddingsConfig


class EmbeddingManager:
    """Manages embeddings for Islamic texts with Arabic text optimization."""
    
    def __init__(self, config: EmbeddingsConfig):
        self.config = config
        self.model = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self._load_model()
        
    def _load_model(self) -> None:
        """Load the embedding model."""
        try:
            logger.info(f"Loading embedding model: {self.config.model_name}")
            self.model = SentenceTransformer(self.config.model_name)
            self.model.to(self.device)
            logger.info(f"Model loaded successfully on {self.device}")
        except Exception as e:
            logger.error(f"Failed to load embedding model: {e}")
            raise
    
    def _preprocess_arabic_text(self, text: str) -> str:
        """Preprocess Arabic text for better embedding quality."""
        try:
            # Reshape Arabic text for proper display
            reshaped_text = arabic_reshaper.reshape(text)
            # Apply bidirectional algorithm
            display_text = get_display(reshaped_text)
            return display_text
        except Exception as e:
            logger.warning(f"Arabic preprocessing failed: {e}")
            return text
    
    def embed_text(self, text: Union[str, List[str]]) -> Union[List[float], List[List[float]]]:
        """Generate embeddings for text(s)."""
        if isinstance(text, str):
            text = [text]
            single_text = True
        else:
            single_text = False
            
        try:
            # Preprocess Arabic texts
            processed_texts = [self._preprocess_arabic_text(t) for t in text]
            
            # Generate embeddings
            embeddings = self.model.encode(
                processed_texts,
                batch_size=self.config.batch_size,
                show_progress_bar=len(processed_texts) > 10,
                convert_to_tensor=False,
                normalize_embeddings=True
            )
            
            if single_text:
                return embeddings[0].tolist()
            else:
                return [emb.tolist() for emb in embeddings]
                
        except Exception as e:
            logger.error(f"Embedding generation failed: {e}")
            raise
    
    def embed_batch(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for a batch of texts."""
        return self.embed_text(texts)
    
    def get_embedding_dimension(self) -> int:
        """Get the dimension of embeddings."""
        return self.model.get_sentence_embedding_dimension()
    
    def health_check(self) -> Dict[str, Any]:
        """Perform health check on embedding manager."""
        try:
            # Test embedding generation
            test_embedding = self.embed_text("بسم الله الرحمن الرحيم")
            
            return {
                "status": "healthy",
                "model_name": self.config.model_name,
                "device": str(self.device),
                "embedding_dimension": self.get_embedding_dimension(),
                "test_embedding_length": len(test_embedding)
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }