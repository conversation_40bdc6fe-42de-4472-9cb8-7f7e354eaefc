#!/usr/bin/env python3
"""Test script for the cleaned English Islamic RAG system."""

import sys
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from islamicRAG.core.rag_system import IslamicRAG
from loguru import logger

def test_system():
    """Test the cleaned system."""
    try:
        logger.info("Testing cleaned English Islamic RAG system...")
        
        # Initialize system
        rag = IslamicRAG()
        rag.initialize()
        logger.info("✓ System initialized successfully")
        
        # Test health check
        health = rag.health_check()
        logger.info(f"✓ Health check: {health['initialized']}")
        
        # Test PDF ingestion
        pdf_path = "data/quran-english-translation-clearquran-edition-allah.pdf"
        rag.ingest_data(pdf_path, "quran")
        logger.info("✓ PDF ingestion completed")
        
        # Test query
        result = rag.query("What does the Quran say about patience?", top_k=2)
        logger.info("✓ Query completed successfully")
        logger.info(f"Answer preview: {result['answer'][:150]}...")
        
        logger.info("🎉 All tests passed! System is working correctly.")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")

if __name__ == "__main__":
    test_system()
