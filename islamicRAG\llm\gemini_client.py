"""Google Gemini client for Islamic RAG system."""

from typing import Dict, Any
import google.generativeai as genai
from loguru import logger
from tenacity import retry, stop_after_attempt, wait_exponential

from ..core.config import LLMConfig


class GeminiClient:
    """Google Gemini client for generating responses."""
    
    def __init__(self, api_key: str, config: LLMConfig):
        self.api_key = api_key
        self.config = config
        self.model = None
        self._configure_client()
    
    def _configure_client(self) -> None:
        """Configure the Gemini client."""
        try:
            genai.configure(api_key=self.api_key)
            
            generation_config = {
                "temperature": self.config.temperature,
                "max_output_tokens": self.config.max_tokens,
            }
            
            self.model = genai.GenerativeModel(
                model_name=self.config.model_name,
                generation_config=generation_config,
                system_instruction=self.config.system_prompt
            )
            
            logger.info(f"Gemini client configured with model: {self.config.model_name}")
            
        except Exception as e:
            logger.error(f"Failed to configure Gemini client: {e}")
            raise
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def generate_response(self, question: str, context: str) -> str:
        """Generate response using Gemini with context."""
        try:
            prompt = self._build_prompt(question, context)
            
            response = self.model.generate_content(prompt)
            
            if response.text:
                return response.text.strip()
            else:
                logger.warning("Empty response from Gemini")
                return "عذراً، لم أتمكن من إنتاج إجابة مناسبة."
                
        except Exception as e:
            logger.error(f"Failed to generate response: {e}")
            raise
    
    def _build_prompt(self, question: str, context: str) -> str:
        """Build the prompt for Gemini."""
        prompt = f"""
السياق المرجعي:
{context}

السؤال: {question}

تعليمات:
- استخدم السياق المرجعي المقدم للإجابة على السؤال
- إذا كان السؤال يتعلق بآية قرآنية، اذكر رقم السورة والآية
- إذا كان السؤال يتعلق بحديث، اذكر المصدر إن أمكن
- كن دقيقاً ومحترماً في إجابتك
- إذا لم تجد إجابة في السياق المقدم، اذكر ذلك بوضوح

الإجابة:
"""
        return prompt
    
    def health_check(self) -> Dict[str, Any]:
        """Perform health check on Gemini client."""
        try:
            # Test with a simple prompt
            test_response = self.model.generate_content("اختبار")
            
            return {
                "status": "healthy",
                "model_name": self.config.model_name,
                "test_response_length": len(test_response.text) if test_response.text else 0
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }