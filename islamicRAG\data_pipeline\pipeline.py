"""PDF ingestion pipeline for English Islamic texts."""

from pathlib import Path
from typing import List, Dict, Any
from loguru import logger
import PyPDF2

from ..chunking.islamic_chunker import IslamicChunker
from ..embeddings.embedding_manager import EmbeddingManager
from ..vector_store.qdrant_store import QdrantVectorStore
from ..core.config import Config


class DataPipeline:
    """PDF ingestion pipeline for English Islamic texts."""
    
    def __init__(self, chunker: IslamicChunker, embedding_manager: EmbeddingManager, 
                 vector_store: QdrantVectorStore, config: Config):
        self.chunker = chunker
        self.embedding_manager = embedding_manager
        self.vector_store = vector_store
        self.config = config
    
    def ingest(self, data_path: str, data_type: str) -> None:
        """Ingest PDF data from file."""
        try:
            logger.info(f"Starting PDF ingestion from {data_path}")

            # Load PDF data
            documents = self._load_pdf(data_path, data_type)

            # Process documents in batches
            batch_size = self.config.get('data_pipeline', {}).get('batch_size', 100)

            for i in range(0, len(documents), batch_size):
                batch = documents[i:i + batch_size]
                self._process_batch(batch)
                logger.info(f"Processed batch {i//batch_size + 1}/{(len(documents)-1)//batch_size + 1}")

            logger.info(f"Successfully ingested {len(documents)} documents")

        except Exception as e:
            logger.error(f"PDF ingestion failed: {e}")
            raise
    


    def _load_pdf(self, data_path: str, data_type: str) -> List[Dict[str, Any]]:
        """Load PDF file and extract text."""
        file_path = Path(data_path)

        if not file_path.exists():
            raise FileNotFoundError(f"PDF file not found: {data_path}")

        if file_path.suffix.lower() != '.pdf':
            raise ValueError(f"Expected PDF file, got: {file_path.suffix}")

        try:
            with open(file_path, 'rb') as f:
                pdf_reader = PyPDF2.PdfReader(f)
                content = ""

                for page_num, page in enumerate(pdf_reader.pages):
                    page_text = page.extract_text()
                    if page_text.strip():
                        content += f"\n--- Page {page_num + 1} ---\n{page_text}\n"

                return [{
                    'content': content.strip(),
                    'metadata': {
                        'source': str(file_path),
                        'type': data_type,
                        'total_pages': len(pdf_reader.pages)
                    }
                }]

        except Exception as e:
            logger.error(f"Failed to extract text from PDF {file_path}: {e}")
            raise
    
    def _process_batch(self, documents: List[Dict[str, Any]]) -> None:
        """Process a batch of documents."""
        processed_docs = []
        
        for doc in documents:
            # Extract content and metadata
            content = doc.get('content', doc.get('text', ''))
            metadata = doc.get('metadata', {})
            
            # Add document-level metadata
            if 'source' not in metadata:
                metadata['source'] = doc.get('source', 'unknown')
            
            # Chunk the document
            chunks = self.chunker.chunk_text(content, metadata)
            
            # Generate embeddings for chunks
            chunk_texts = [chunk['content'] for chunk in chunks]
            embeddings = self.embedding_manager.embed_batch(chunk_texts)
            
            # Prepare documents for vector store
            for chunk, embedding in zip(chunks, embeddings):
                processed_doc = {
                    'content': chunk['content'],
                    'embedding': embedding,
                    'metadata': chunk['metadata']
                }
                processed_docs.append(processed_doc)
        
        # Add to vector store
        if processed_docs:
            self.vector_store.add_documents(processed_docs)
    
    def validate_document(self, doc: Dict[str, Any]) -> bool:
        """Validate document structure."""
        required_fields = ['content']
        
        for field in required_fields:
            if field not in doc and 'text' not in doc:
                logger.warning(f"Document missing required field: {field}")
                return False
        
        return True