"""Data ingestion pipeline for Islamic texts."""

import json
import csv
import xml.etree.ElementTree as ET
from pathlib import Path
from typing import List, Dict, Any
from loguru import logger

from ..chunking.islamic_chunker import IslamicChunker
from ..embeddings.embedding_manager import EmbeddingManager
from ..vector_store.qdrant_store import QdrantVectorStore
from ..core.config import Config


class DataPipeline:
    """Data ingestion pipeline for Islamic texts."""
    
    def __init__(self, chunker: IslamicChunker, embedding_manager: EmbeddingManager, 
                 vector_store: QdrantVectorStore, config: Config):
        self.chunker = chunker
        self.embedding_manager = embedding_manager
        self.vector_store = vector_store
        self.config = config
    
    def ingest(self, data_path: str, data_type: str) -> None:
        """Ingest data from file."""
        try:
            logger.info(f"Starting data ingestion from {data_path}")
            
            # Load data
            documents = self._load_data(data_path, data_type)
            
            # Process documents in batches
            batch_size = self.config.get('data_pipeline', {}).get('batch_size', 100)
            
            for i in range(0, len(documents), batch_size):
                batch = documents[i:i + batch_size]
                self._process_batch(batch)
                logger.info(f"Processed batch {i//batch_size + 1}/{(len(documents)-1)//batch_size + 1}")
            
            logger.info(f"Successfully ingested {len(documents)} documents")
            
        except Exception as e:
            logger.error(f"Data ingestion failed: {e}")
            raise
    
    def _load_data(self, data_path: str, data_type: str) -> List[Dict[str, Any]]:
        """Load data from file based on format."""
        file_path = Path(data_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"Data file not found: {data_path}")
        
        if file_path.suffix.lower() == '.json':
            return self._load_json(file_path)
        elif file_path.suffix.lower() == '.jsonl':
            return self._load_jsonl(file_path)
        elif file_path.suffix.lower() == '.csv':
            return self._load_csv(file_path)
        elif file_path.suffix.lower() == '.xml':
            return self._load_xml(file_path)
        elif file_path.suffix.lower() == '.txt':
            return self._load_txt(file_path, data_type)
        else:
            raise ValueError(f"Unsupported file format: {file_path.suffix}")
    
    def _load_json(self, file_path: Path) -> List[Dict[str, Any]]:
        """Load JSON file."""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if isinstance(data, list):
            return data
        else:
            return [data]
    
    def _load_jsonl(self, file_path: Path) -> List[Dict[str, Any]]:
        """Load JSONL file."""
        documents = []
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    documents.append(json.loads(line))
        return documents
    
    def _load_csv(self, file_path: Path) -> List[Dict[str, Any]]:
        """Load CSV file."""
        documents = []
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                documents.append(row)
        return documents
    
    def _load_xml(self, file_path: Path) -> List[Dict[str, Any]]:
        """Load XML file."""
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        documents = []
        for elem in root:
            doc = {}
            for child in elem:
                doc[child.tag] = child.text
            documents.append(doc)
        
        return documents
    
    def _load_txt(self, file_path: Path, data_type: str) -> List[Dict[str, Any]]:
        """Load plain text file."""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        return [{
            'content': content,
            'metadata': {
                'source': str(file_path),
                'type': data_type
            }
        }]
    
    def _process_batch(self, documents: List[Dict[str, Any]]) -> None:
        """Process a batch of documents."""
        processed_docs = []
        
        for doc in documents:
            # Extract content and metadata
            content = doc.get('content', doc.get('text', ''))
            metadata = doc.get('metadata', {})
            
            # Add document-level metadata
            if 'source' not in metadata:
                metadata['source'] = doc.get('source', 'unknown')
            
            # Chunk the document
            chunks = self.chunker.chunk_text(content, metadata)
            
            # Generate embeddings for chunks
            chunk_texts = [chunk['content'] for chunk in chunks]
            embeddings = self.embedding_manager.embed_batch(chunk_texts)
            
            # Prepare documents for vector store
            for chunk, embedding in zip(chunks, embeddings):
                processed_doc = {
                    'content': chunk['content'],
                    'embedding': embedding,
                    'metadata': chunk['metadata']
                }
                processed_docs.append(processed_doc)
        
        # Add to vector store
        if processed_docs:
            self.vector_store.add_documents(processed_docs)
    
    def validate_document(self, doc: Dict[str, Any]) -> bool:
        """Validate document structure."""
        required_fields = ['content']
        
        for field in required_fields:
            if field not in doc and 'text' not in doc:
                logger.warning(f"Document missing required field: {field}")
                return False
        
        return True