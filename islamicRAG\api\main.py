"""FastAPI application for English Islamic RAG system."""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks, File, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import uvicorn
from loguru import logger
import tempfile
import os

from ..core.rag_system import IslamicRAG


# Pydantic models
class QueryRequest(BaseModel):
    question: str
    top_k: int = 5
    filters: Optional[Dict[str, Any]] = None


class QueryResponse(BaseModel):
    question: str
    answer: str
    sources: List[Dict[str, Any]]
    metadata: Dict[str, Any]


class IngestRequest(BaseModel):
    data_path: str
    data_type: str


class IngestResponse(BaseModel):
    message: str
    status: str
    file_info: Optional[Dict[str, Any]] = None


class HealthResponse(BaseModel):
    status: str
    components: Dict[str, Any]


# Initialize FastAPI app
app = FastAPI(
    title="English Islamic RAG API",
    description="Production-ready RAG system for English Islamic texts (Quran)",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global RAG system instance
rag_system: Optional[IslamicRAG] = None


@app.on_event("startup")
async def startup_event():
    """Initialize the RAG system on startup."""
    global rag_system
    try:
        logger.info("Initializing Islamic RAG system...")
        rag_system = IslamicRAG()
        rag_system.initialize()
        logger.info("Islamic RAG system initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize RAG system: {e}")
        raise


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    if rag_system is None:
        raise HTTPException(status_code=503, detail="RAG system not initialized")
    
    try:
        health_status = rag_system.health_check()
        return HealthResponse(**health_status)
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/query", response_model=QueryResponse)
async def query_rag(request: QueryRequest):
    """Query the Islamic RAG system."""
    if rag_system is None:
        raise HTTPException(status_code=503, detail="RAG system not initialized")
    
    try:
        result = rag_system.query(
            question=request.question,
            top_k=request.top_k
        )
        return QueryResponse(**result)
    except Exception as e:
        logger.error(f"Query failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/ingest")
async def ingest_data(request: IngestRequest, background_tasks: BackgroundTasks):
    """Ingest data into the RAG system from file path."""
    if rag_system is None:
        raise HTTPException(status_code=503, detail="RAG system not initialized")

    try:
        # Run ingestion in background
        background_tasks.add_task(
            rag_system.ingest_data,
            request.data_path,
            request.data_type
        )

        return IngestResponse(
            message="Data ingestion started",
            status="processing",
            file_info={"path": request.data_path, "type": request.data_type}
        )
    except Exception as e:
        logger.error(f"Ingestion failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/ingest/upload", response_model=IngestResponse)
async def ingest_upload(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    data_type: str = "quran"
):
    """Upload and ingest a PDF file into the RAG system."""
    if rag_system is None:
        raise HTTPException(status_code=503, detail="RAG system not initialized")

    # Validate file type
    if not file.filename.lower().endswith('.pdf'):
        raise HTTPException(status_code=400, detail="Only PDF files are supported")

    try:
        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name

        # Run ingestion in background
        background_tasks.add_task(
            _ingest_and_cleanup,
            rag_system,
            temp_file_path,
            data_type
        )

        return IngestResponse(
            message=f"PDF file '{file.filename}' uploaded and ingestion started",
            status="processing",
            file_info={
                "filename": file.filename,
                "size": len(content),
                "type": data_type
            }
        )
    except Exception as e:
        logger.error(f"PDF upload and ingestion failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def _ingest_and_cleanup(rag_system: IslamicRAG, file_path: str, data_type: str):
    """Helper function to ingest data and cleanup temporary file."""
    try:
        rag_system.ingest_data(file_path, data_type)
        logger.info(f"Successfully ingested data from {file_path}")
    except Exception as e:
        logger.error(f"Ingestion failed for {file_path}: {e}")
    finally:
        # Cleanup temporary file
        try:
            os.unlink(file_path)
            logger.info(f"Cleaned up temporary file: {file_path}")
        except Exception as e:
            logger.warning(f"Failed to cleanup temporary file {file_path}: {e}")


@app.get("/collection/info")
async def get_collection_info():
    """Get vector store collection information."""
    if rag_system is None:
        raise HTTPException(status_code=503, detail="RAG system not initialized")
    
    try:
        info = rag_system.vector_store.get_collection_info()
        return info
    except Exception as e:
        logger.error(f"Failed to get collection info: {e}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    print("\nAPI Documentation will be available at: http://localhost:8000/docs")
    print("API Server will be available at: http://localhost:8000\n")
    uvicorn.run(
        "islamicRAG.api.main:app",
        host="localhost",
        port=8000,
        reload=False
    )