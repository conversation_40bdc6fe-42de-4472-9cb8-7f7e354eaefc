"""Tests for the main RAG system."""

import pytest
from unittest.mock import Mock, patch

from islamicRAG.core.rag_system import IslamicRAG
from islamicRAG.core.config import Config


class TestIslamicRAG:
    """Test cases for Islamic RAG system."""
    
    @pytest.fixture
    def mock_config(self):
        """Mock configuration."""
        config = Mock(spec=Config)
        config.api_keys.gemini_api_key = "test_key"
        config.embeddings.model_name = "test_model"
        config.vector_store.url = "test_url"
        config.llm.model_name = "gemini-2.0-flash"
        config.chunking.strategy = "islamic_aware"
        return config
    
    @patch('islamicRAG.core.rag_system.EmbeddingManager')
    @patch('islamicRAG.core.rag_system.QdrantVectorStore')
    @patch('islamicRAG.core.rag_system.IslamicChunker')
    @patch('islamicRAG.core.rag_system.GeminiClient')
    @patch('islamicRAG.core.rag_system.DataPipeline')
    def test_initialization(self, mock_pipeline, mock_gemini, mock_chunker, 
                          mock_vector_store, mock_embedding, mock_config):
        """Test RAG system initialization."""
        with patch('islamicRAG.core.rag_system.Config', return_value=mock_config):
            rag = IslamicRAG()
            rag.initialize()
            
            assert rag._initialized is True
            assert rag.embedding_manager is not None
            assert rag.vector_store is not None
            assert rag.chunker is not None
            assert rag.llm_client is not None
            assert rag.data_pipeline is not None
    
    def test_query_without_initialization(self):
        """Test querying without initialization raises error."""
        rag = IslamicRAG()
        
        with pytest.raises(RuntimeError, match="System not initialized"):
            rag.query("test question")
    
    @patch('islamicRAG.core.rag_system.Config')
    def test_health_check(self, mock_config_class):
        """Test health check functionality."""
        mock_config_class.return_value = Mock()
        rag = IslamicRAG()
        
        health_status = rag.health_check()
        
        assert "initialized" in health_status
        assert health_status["initialized"] is False