#!/usr/bin/env python3
"""Test runner for Islamic RAG system."""

import subprocess
import sys


def run_tests():
    """Run all tests."""
    test_commands = [
        ["python", "-m", "pytest", "../tests/", "-v"],
        ["python", "-m", "pytest", "tests/", "--cov=islamicRAG", "--cov-report=html"]
    ]
    
    for cmd in test_commands:
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"Test failed: {result.stderr}")
            return False
        else:
            print("Tests passed!")
    
    return True


if __name__ == "__main__":
    if run_tests():
        print("All tests passed!")
        sys.exit(0)
    else:
        print("Some tests failed!")
        sys.exit(1)