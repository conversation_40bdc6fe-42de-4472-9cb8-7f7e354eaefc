#!/usr/bin/env python3
"""Test script to verify each component is working."""

import sys
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from islamicRAG.core.config import Config
from islamicRAG.embeddings.embedding_manager import EmbeddingManager
from islamicRAG.vector_store.qdrant_store import QdrantVectorStore
from islamicRAG.chunking.islamic_chunker import IslamicChunker
from islamicRAG.llm.gemini_client import GeminiClient


def test_config():
    """Test configuration loading."""
    print("🔧 Testing Configuration...")
    try:
        config = Config()
        print(f"✅ Config loaded successfully")
        print(f"   - Gemini API Key: {'✅ Set' if config.api_keys.gemini_api_key else '❌ Missing'}")
        print(f"   - Vector Store: {config.vector_store.url}")
        print(f"   - Embedding Model: {config.embeddings.model_name}")
        return config
    except Exception as e:
        print(f"❌ Config failed: {e}")
        return None


def test_embeddings(config):
    """Test embedding manager."""
    print("\n📊 Testing Embeddings...")
    try:
        embedding_manager = EmbeddingManager(config.embeddings)
        
        # Test Arabic text
        test_text = "بسم الله الرحمن الرحيم"
        embedding = embedding_manager.embed_text(test_text)
        
        print(f"✅ Embeddings working")
        print(f"   - Model: {config.embeddings.model_name}")
        print(f"   - Dimension: {len(embedding)}")
        print(f"   - Device: {embedding_manager.device}")
        
        # Health check
        health = embedding_manager.health_check()
        print(f"   - Health: {health['status']}")
        
        return embedding_manager
    except Exception as e:
        print(f"❌ Embeddings failed: {e}")
        return None


def test_vector_store(config):
    """Test vector store connection."""
    print("\n🗄️ Testing Vector Store...")
    try:
        vector_store = QdrantVectorStore(config.vector_store)
        
        # Health check
        health = vector_store.health_check()
        print(f"✅ Vector store connected")
        print(f"   - URL: {config.vector_store.url}")
        print(f"   - Health: {health['status']}")
        print(f"   - Collection: {health.get('collection_name', 'N/A')}")
        
        # Collection info
        info = vector_store.get_collection_info()
        if info:
            print(f"   - Points: {info.get('points_count', 0)}")
        
        return vector_store
    except Exception as e:
        print(f"❌ Vector store failed: {e}")
        print("   💡 Make sure Qdrant is running: docker-compose up qdrant -d")
        return None


def test_chunker(config):
    """Test Islamic chunker."""
    print("\n✂️ Testing Chunker...")
    try:
        chunker = IslamicChunker(config.chunking)
        
        # Test with Quran verse
        test_text = "بسم الله الرحمن الرحيم {1} الحمد لله رب العالمين {2} الرحمن الرحيم {3}"
        chunks = chunker.chunk_text(test_text, {"type": "quran", "surah": "Al-Fatiha"})
        
        print(f"✅ Chunker working")
        print(f"   - Strategy: {config.chunking.strategy}")
        print(f"   - Test chunks created: {len(chunks)}")
        print(f"   - Sample chunk: {chunks[0]['content'][:50]}...")
        
        return chunker
    except Exception as e:
        print(f"❌ Chunker failed: {e}")
        return None


def test_llm(config):
    """Test LLM client."""
    print("\n🤖 Testing LLM Client...")
    try:
        llm_client = GeminiClient(config.api_keys.gemini_api_key, config.llm)
        
        # Health check
        health = llm_client.health_check()
        print(f"✅ LLM client working")
        print(f"   - Model: {config.llm.model_name}")
        print(f"   - Health: {health['status']}")
        
        # Test generation
        test_response = llm_client.generate_response(
            "ما معنى البسملة؟",
            "البسملة هي قول: بسم الله الرحمن الرحيم"
        )
        print(f"   - Test response length: {len(test_response)} chars")
        
        return llm_client
    except Exception as e:
        print(f"❌ LLM client failed: {e}")
        print("   💡 Check your Gemini API key in config/config.yaml or .env")
        return None


def main():
    """Run all component tests."""
    print("🚀 Islamic RAG System Component Tests\n")
    
    # Test each component
    config = test_config()
    if not config:
        return False
    
    embedding_manager = test_embeddings(config)
    vector_store = test_vector_store(config)
    chunker = test_chunker(config)
    llm_client = test_llm(config)
    
    # Summary
    print("\n📋 Test Summary:")
    components = {
        "Configuration": config is not None,
        "Embeddings": embedding_manager is not None,
        "Vector Store": vector_store is not None,
        "Chunker": chunker is not None,
        "LLM Client": llm_client is not None
    }
    
    for component, status in components.items():
        status_icon = "✅" if status else "❌"
        print(f"   {status_icon} {component}")
    
    all_working = all(components.values())
    
    if all_working:
        print("\n🎉 All components are working! You can now:")
        print("   1. Add data to the data/ folders")
        print("   2. Run ingestion: python scripts/ingest_sample_data.py")
        print("   3. Start the API: python -m islamicRAG.api.main")
    else:
        print("\n⚠️ Some components need attention. Check the errors above.")
    
    return all_working


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)